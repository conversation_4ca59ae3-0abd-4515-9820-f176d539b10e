import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Header } from '@heroui/react'
import { MapPin, MessageCircle, Star, Globe, Shield, Clock, Truck, ArrowRight, Zap, Target, Sparkles, Sun, Moon } from 'lucide-react'
import { Cover } from '../ui/cover'
import { TextGenerateEffect } from '../ui/text-generate-effect'
import { WorldMapWrapper } from './WorldMapWrapper'
import { FlickeringGrid } from '../magicui/flickering-grid'

export const LandingPage = () => {
  return (
    <div className='min-h-screen bg-background overflow-hidden'>
      <h1 className='text-4xl md:text-4xl lg:text-6xl font-semibold max-w-7xl mx-auto text-center mt-6 relative z-20 py-6 bg-clip-text text-transparent bg-gradient-to-b from-neutral-800 via-neutral-700 to-neutral-700 dark:from-neutral-800 dark:via-white dark:to-white'>
        <TextGenerateEffect duration={1} words='Встречайте доставку в 5 раз' /> <Cover>быстрее</Cover>
      </h1>

      <WorldMapWrapper/>

      <section className='relative min-h-screen flex items-center overflow-hidden pt-20'>
        <div>
          <FlickeringGrid
            className='absolute inset-0 z-0 size-full w-full flex'
            squareSize={4}
            gridGap={6}
            color='#6B7280'
            maxOpacity={0.5}
            flickerChance={0.1}
            // height={1024}
            // width={800}
          />
        </div>
        {/* Animated background elements */}
        <div className='absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-secondary/5'></div>
        <div className='absolute top-20 right-10 w-72 h-72 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-3xl floating'></div>
        <div
          className='absolute bottom-20 left-10 w-96 h-96 bg-gradient-to-tr from-secondary/15 to-accent/15 rounded-full blur-3xl floating'
          style={{ animationDelay: '-3s' }}
        ></div>

        <div className='container mx-auto px-6 relative z-10'>
          <div className='grid lg:grid-cols-2 gap-16 items-center'>
            {/* Left side - Brutalist typography */}
            <div className='space-y-8'>
              <div className='space-y-6'>
                <h1 className='text-6xl lg:text-8xl font-black leading-none text-balance'>
                  <span className='block text-foreground'>TAKE</span>
                  <span className='block gradient-text'>N'PASS</span>
                </h1>

                <div className='relative'>
                  <div className='absolute -left-4 top-0 w-1 h-full bg-gradient-to-b from-secondary to-accent'></div>
                  <p className='text-xl lg:text-2xl text-muted-foreground leading-relaxed pl-8 font-medium'>
                    Революционная платформа краудшиппинга.
                    <span className='text-primary font-bold'> Доставляй быстрее.</span>
                    <span className='text-primary font-bold'> Зарабатывай больше.</span>
                  </p>
                </div>
              </div>

              <div className='flex flex-col sm:flex-row gap-6'>
                <Button
                  size='lg'
                  className='bg-primary hover:bg-primary/90 text-primary-foreground font-black px-8 py-4 text-lg shadow-lg hover:shadow-xl hover:translate-x-2 hover:translate-y-2 transition-all duration-300 group'
                >
                  <Target className='w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300' />
                  НАЙТИ ДОСТАВКУ
                </Button>
                <Button
                  size='lg'
                  variant='bordered'
                  className='glass-card border-2 border-secondary text-primary hover:bg-secondary hover:text-secondary-foreground font-bold px-8 py-4 text-lg group bg-transparent'
                >
                  <Zap className='w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300' />
                  СТАТЬ КУРЬЕРОМ
                </Button>
              </div>
            </div>

            {/* Right side - 3D geometric shapes */}
            <div className='relative lg:block hidden'>
              <div className='relative w-full h-96'>
                {/* Floating geometric elements */}
                <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary to-secondary rounded-3xl brutalist-shadow floating transform rotate-12'></div>
                <div
                  className='absolute top-20 left-10 w-24 h-24 bg-gradient-to-br from-secondary to-accent rounded-2xl brutalist-shadow floating transform -rotate-12'
                  style={{ animationDelay: '-2s' }}
                ></div>
                <div className='absolute bottom-10 right-20 w-40 h-40 glass-card rounded-3xl brutalist-shadow floating transform rotate-6' style={{ animationDelay: '-4s' }}></div>
                <div
                  className='absolute bottom-0 left-0 w-28 h-28 bg-gradient-to-br from-accent to-primary rounded-full brutalist-shadow floating'
                  style={{ animationDelay: '-1s' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id='features' className='py-32 relative'>
        <div className='absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5'></div>
        <div className='container mx-auto px-6 relative z-10'>
          <div className='text-center mb-20'>
            <h2 className='text-5xl lg:text-6xl font-black text-foreground mb-6 text-balance'>
              ПОЧЕМУ <span className='gradient-text'>TAKENPASS</span>?
            </h2>
            <div className='w-24 h-1 bg-gradient-to-r from-secondary to-accent mx-auto mb-6'></div>
            <p className='text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed'>Передовые технологии встречают человеческие потребности</p>
          </div>

          {/* Asymmetric grid layout */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {/* Large feature card */}
            <div className='lg:col-span-2 lg:row-span-2'>
              <Card className='glass-card h-full p-8 hover:translate-x-2 hover:translate-y-2 hover:shadow-none brutalist-shadow transition-all duration-300 group'>
                <CardHeader className='p-0'>
                  <div className='flex items-center space-x-4 mb-6'>
                    <div className='w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-300'>
                      <MapPin className='w-8 h-8 text-primary-foreground' />
                    </div>
                    <div>
                      <div className='text-3xl font-black text-foreground'>ИНТЕРАКТИВНЫЕ КАРТЫ</div>
                      <div className='w-16 h-1 bg-gradient-to-r from-secondary to-accent mt-2'></div>
                    </div>
                  </div>
                  <CardFooter className='text-lg text-muted-foreground leading-relaxed'>
                    Визуализация маршрутов в реальном времени с точным отслеживанием местоположения. AI-powered оптимизация путей и предсказание времени доставки.
                  </CardFooter>
                </CardHeader>
              </Card>
            </div>

            {/* Smaller feature cards */}
            <Card className='glass-card p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-none brutalist-shadow transition-all duration-300 group'>
              <CardHeader className='p-0'>
                <div className='w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                  <MessageCircle className='w-6 h-6 text-secondary-foreground' />
                </div>
                <div className='text-xl font-black text-foreground'>REAL-TIME ЧАТ</div>
                <CardFooter className='text-muted-foreground mt-2'>Мгновенное общение с шифрованием</CardFooter>
              </CardHeader>
            </Card>

            <Card className='glass-card p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-none brutalist-shadow transition-all duration-300 group'>
              <CardHeader className='p-0'>
                <div className='w-12 h-12 bg-gradient-to-br from-accent to-primary rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                  <Star className='w-6 h-6 text-accent-foreground' />
                </div>
                <div className='text-xl font-black text-foreground'>БЛОКЧЕЙН РЕЙТИНГИ</div>
                <CardFooter className='text-muted-foreground mt-2'>Неизменяемые отзывы и репутация</CardFooter>
              </CardHeader>
            </Card>

            <Card className='glass-card p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-none brutalist-shadow transition-all duration-300 group'>
              <CardHeader className='p-0'>
                <div className='w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                  <Shield className='w-6 h-6 text-primary-foreground' />
                </div>
                <div className='text-xl font-black text-foreground'>AI БЕЗОПАСНОСТЬ</div>
                <CardFooter className='text-muted-foreground mt-2'>Машинное обучение для защиты</CardFooter>
              </CardHeader>
            </Card>

            <Card className='glass-card p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-none brutalist-shadow transition-all duration-300 group'>
              <CardHeader className='p-0'>
                <div className='w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                  <Globe className='w-6 h-6 text-secondary-foreground' />
                </div>
                <div className='text-xl font-black text-foreground'>ГЛОБАЛЬНАЯ СЕТЬ</div>
                <CardFooter className='text-muted-foreground mt-2'>150+ стран, миллионы пользователей</CardFooter>
              </CardHeader>
            </Card>

            <Card className='glass-card p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-none brutalist-shadow transition-all duration-300 group'>
              <CardHeader className='p-0'>
                <div className='w-12 h-12 bg-gradient-to-br from-accent to-primary rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                  <Clock className='w-6 h-6 text-accent-foreground' />
                </div>
                <div className='text-xl font-black text-foreground'>МГНОВЕННАЯ ДОСТАВКА</div>
                <CardFooter className='text-muted-foreground mt-2'>Средняя скорость: 3x быстрее</CardFooter>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      <section id='process' className='py-32 relative overflow-hidden'>
        <div className='absolute inset-0 bg-gradient-to-bl from-background to-primary/10'></div>
        <div className='container mx-auto px-6 relative z-10'>
          <div className='text-center mb-20'>
            <h2 className='text-5xl lg:text-6xl font-black text-foreground mb-6'>
              КАК ЭТО <span className='gradient-text'>РАБОТАЕТ</span>
            </h2>
            <div className='w-32 h-2 bg-gradient-to-r from-secondary to-accent mx-auto'></div>
          </div>

          <div className='grid lg:grid-cols-2 gap-20'>
            {/* Senders */}
            <div className='space-y-8'>
              <div className='flex items-center space-x-4 mb-12'>
                <div className='w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center brutalist-shadow'>
                  <span className='text-2xl'>📦</span>
                </div>
                <h3 className='text-4xl font-black text-foreground'>ОТПРАВИТЕЛИ</h3>
              </div>

              <div className='space-y-8'>
                {[
                  { num: '01', title: 'СОЗДАЙ ЗАЯВКУ', desc: 'AI подберет оптимальный маршрут' },
                  { num: '02', title: 'ВЫБЕРИ КУРЬЕРА', desc: 'Проверенные путешественники с рейтингом' },
                  { num: '03', title: 'ОТСЛЕЖИВАЙ', desc: 'Real-time GPS + blockchain верификация' }
                ].map((step, i) => (
                  <div key={i} className='flex items-start space-x-6 group'>
                    <div className='w-16 h-16 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center brutalist-shadow group-hover:translate-x-1 group-hover:translate-y-1 group-hover:shadow-none transition-all duration-300'>
                      <span className='text-secondary-foreground font-black text-lg'>{step.num}</span>
                    </div>
                    <div className='flex-1'>
                      <h4 className='text-2xl font-black text-foreground mb-2'>{step.title}</h4>
                      <p className='text-muted-foreground text-lg'>{step.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Carriers */}
            <div className='space-y-8'>
              <div className='flex items-center space-x-4 mb-12'>
                <div className='w-16 h-16 bg-gradient-to-br from-accent to-primary rounded-2xl flex items-center justify-center brutalist-shadow'>
                  <span className='text-2xl'>✈️</span>
                </div>
                <h3 className='text-4xl font-black text-foreground'>КУРЬЕРЫ</h3>
              </div>

              <div className='space-y-8'>
                {[
                  { num: '01', title: 'УКАЖИ МАРШРУТ', desc: 'Автоматическое сопоставление с заявками' },
                  { num: '02', title: 'НАЙДИ ЗАКАЗЫ', desc: 'AI рекомендации по доходности' },
                  { num: '03', title: 'ЗАРАБОТАЙ', desc: 'Мгновенные выплаты + бонусы' }
                ].map((step, i) => (
                  <div key={i} className='flex items-start space-x-6 group'>
                    <div className='w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center brutalist-shadow group-hover:translate-x-1 group-hover:translate-y-1 group-hover:shadow-none transition-all duration-300'>
                      <span className='text-primary-foreground font-black text-lg'>{step.num}</span>
                    </div>
                    <div className='flex-1'>
                      <h4 className='text-2xl font-black text-foreground mb-2'>{step.title}</h4>
                      <p className='text-muted-foreground text-lg'>{step.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id='community' className='py-32 relative'>
        <div className='absolute inset-0 bg-gradient-to-r from-secondary/5 to-accent/5'></div>
        <div className='container mx-auto px-6 relative z-10'>
          <div className='text-center mb-20'>
            <h2 className='text-5xl lg:text-6xl font-black text-foreground mb-6'>
              НАШЕ <span className='gradient-text'>СООБЩЕСТВО</span>
            </h2>
            <div className='w-24 h-1 bg-gradient-to-r from-secondary to-accent mx-auto'></div>
          </div>

          <div className='grid md:grid-cols-3 gap-8 mb-20'>
            {[
              { value: '2.5M+', label: 'АКТИВНЫХ ПОЛЬЗОВАТЕЛЕЙ', color: 'from-primary to-secondary' },
              { value: '15M+', label: 'УСПЕШНЫХ ДОСТАВОК', color: 'from-secondary to-accent' },
              { value: '180+', label: 'СТРАН И ГОРОДОВ', color: 'from-accent to-primary' }
            ].map((stat, i) => (
              <div key={i} className='glass-card p-8 text-center brutalist-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300'>
                <div className='text-6xl font-black text-secondary mb-4'>{stat.value}</div>
                <div className='text-muted-foreground font-bold text-lg'>{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Testimonials */}
          <div className='grid md:grid-cols-3 gap-8'>
            {[
              {
                name: 'Анна М.',
                rating: 5,
                text: 'Революционная платформа! Доставка из Москвы в Берлин за 18 часов. Невероятно!'
              },
              {
                name: 'Михаил К.',
                rating: 5,
                text: 'Зарабатываю $500+ в месяц на путешествиях. TakeNPass изменил мою жизнь.'
              },
              {
                name: 'Елена С.',
                rating: 5,
                text: 'Безопасность на высшем уровне. Блокчейн-верификация дает полное спокойствие.'
              }
            ].map((testimonial, i) => (
              <Card key={i} className='glass-card p-6 brutalist-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300'>
                <CardHeader className='p-0'>
                  <div className='flex items-center space-x-3 mb-4'>
                    <div className='w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center'>
                      <span className='text-primary-foreground font-bold'>
                        {testimonial.name
                          .split(' ')
                          .map((n) => n[0])
                          .join('')}
                      </span>
                    </div>
                    <div>
                      <div className='font-black text-foreground'>{testimonial.name}</div>
                      <div className='flex'>
                        {[...Array(testimonial.rating)].map((_, j) => (
                          <Star key={j} className='w-4 h-4 fill-accent text-accent' />
                        ))}
                      </div>
                    </div>
                  </div>
                  <CardFooter className='text-muted-foreground text-base leading-relaxed'>"{testimonial.text}"</CardFooter>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className='py-32 relative overflow-hidden'>
        <div className='absolute inset-0 bg-gradient-to-br from-primary via-secondary to-accent'></div>
        <div className='absolute inset-0 bg-black/20'></div>
        <div className='container mx-auto px-6 text-center relative z-10'>
          <h2 className='text-6xl lg:text-8xl font-black text-white mb-8 text-balance'>
            ГОТОВ К <span className='text-black'>БУДУЩЕМУ</span>?
          </h2>
          <p className='text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed'>Присоединяйся к революции краудшиппинга. Более 2.5 миллионов пользователей уже с нами.</p>
          <div className='flex flex-col sm:flex-row gap-6 justify-center'>
            <Button
              size='lg'
              className='bg-background hover:bg-muted text-foreground font-black px-12 py-6 text-xl brutalist-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 group border-2 border-foreground'
            >
              <ArrowRight className='w-6 h-6 mr-3 group-hover:translate-x-2 transition-transform duration-300' />
              НАЧАТЬ СЕЙЧАС
            </Button>
            <Button
              size='lg'
              variant='bordered'
              className='border-4 border-background text-background hover:bg-background hover:text-foreground font-black px-12 py-6 text-xl bg-transparent'
            >
              УЗНАТЬ БОЛЬШЕ
            </Button>
          </div>
        </div>
      </section>

      <footer className='glass border-t border-border py-16'>
        <div className='container mx-auto px-6'>
          <div className='grid md:grid-cols-4 gap-12 mb-12'>
            <div className='md:col-span-2'>
              <div className='flex items-center space-x-3 mb-6'>
                <div className='w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center'>
                  <Truck className='w-7 h-7 text-primary-foreground' />
                </div>
                <span className='text-3xl font-black text-foreground'>TakeNPass</span>
              </div>
              <p className='text-muted-foreground text-lg leading-relaxed max-w-md'>Революционная платформа краудшиппинга для быстрой и выгодной доставки по всему миру.</p>
            </div>

            <div>
              <h4 className='font-black text-foreground mb-6 text-lg'>ПЛАТФОРМА</h4>
              <ul className='space-y-3'>
                {['Как это работает', 'Безопасность', 'Тарифы', 'API'].map((item, i) => (
                  <li key={i}>
                    <a href='#' className='text-muted-foreground hover:text-secondary transition-colors font-medium'>
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className='font-black text-foreground mb-6 text-lg'>КОМПАНИЯ</h4>
              <ul className='space-y-3'>
                {['О нас', 'Блог', 'Карьера', 'Пресс-центр'].map((item, i) => (
                  <li key={i}>
                    <a href='#' className='text-muted-foreground hover:text-secondary transition-colors font-medium'>
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className='border-t border-border pt-8 flex flex-col md:flex-row justify-between items-center'>
            <p className='text-muted-foreground'>© 2025 TakeNPass. Все права защищены.</p>
            <div className='flex space-x-8 mt-4 md:mt-0'>
              <a href='#' className='text-muted-foreground hover:text-secondary transition-colors'>
                Политика конфиденциальности
              </a>
              <a href='#' className='text-muted-foreground hover:text-secondary transition-colors'>
                Условия использования
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
