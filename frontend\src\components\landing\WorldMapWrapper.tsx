import { Case } from '@/types'
import { useEffect, useState } from 'react'
import WorldMap from '../ui/world-map'
import i18next from 'i18next'
import { trpc } from '@/trpc'

type MapDots =
  | {
      start: {
        lat: number
        lng: number
        label?: string
      }
      end: {
        lat: number
        lng: number
        label?: string
      }
    }[]
  | undefined

export const WorldMapWrapper = () => {
  const [dots, setDots] = useState<MapDots>([])

  const {
    data,
    isFetching,
    isError,
    error,
    isSuccess
    // refetch
  } = trpc.case.list.useQuery(
    {
      limit: 100,
      page: 1,
      radius: 5000,
      lang: i18next.language
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false
    }
  )

  useEffect(() => {
    if (isSuccess) {
      const dots = data.cases.map((caseItem) => ({
        start: {
          lat: Number(caseItem?.from?.lat),
          lng: Number(caseItem?.from?.lon),
          label: caseItem?.from?.geometa?.address?.location
        },
        end: {
          lat: Number(caseItem?.to?.lat),
          lng: Number(caseItem?.to?.lon),
          label: caseItem?.to?.geometa?.address?.location
        }
      }))
      setDots(dots)
    }
  }, [data])

  return (
    <>
      <WorldMap dots={dots} />
    </>
  )
}
